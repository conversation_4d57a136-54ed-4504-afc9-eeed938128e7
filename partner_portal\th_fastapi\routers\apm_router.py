from typing import Annotated, List
import time
import logging
from ..schemas import InternshipConditionsData
from ..schemas import ApmCampaignDatas, APMLeadDatas, ApmTraitDatas, ApmTeamDatas, SaleOrderDatas, RecordDatas, \
    CreateRecordDatas, ApmContactTraitDatas,ApmTraitValueDatas
from odoo.api import Environment
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks, Path, Query
from ..dependencies import fastapi_endpoint, odoo_env, authenticated_fastapi_endpoint
from odoo.addons.fastapi.models.fastapi_endpoint import FastapiEndpoint as ThFastapi
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)

router = APIRouter(tags=["apm"])


@router.post("/api/apmcampaign")
def create_apm_campaign(
        apm_campaign_data: ApmCampaignDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            apm_campaign = fastapi.env['th.apm.campaign'].sudo().with_context(th_sync=True).create(
                apm_campaign_data.model_dump(exclude_unset=True,exclude_none=True))
            return {
                'id': apm_campaign.id
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/apmcampaign/{id}")
def write_apm_campaign(
        apm_campaign_data: ApmCampaignDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            apm_campaign = fastapi.env['th.apm.campaign'].browse(id)
            if not apm_campaign.exists():
                raise HTTPException(status_code=404, detail="APM Campaign not found.")
            data_to_update = apm_campaign_data.model_dump(exclude_unset=True,exclude_none=True)
            apm_campaign.sudo().with_context(th_sync=True).write(data_to_update)
            return {
                'id': apm_campaign.id,
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/apmcampaign/{id}")
def delete_apm_campaign(
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            apm_campaign = fastapi.env['th.apm.campaign'].browse(id)
            if not apm_campaign.exists():
                raise HTTPException(status_code=404, detail="APM Campaign not found.")
            apm_campaign.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/apmleads")
def create_apm_lead(
        records: list[RecordDatas],
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]):
    try:
        if not fastapi:
            raise HTTPException(status_code=401, detail="Authentication required")

        return th_process_apm_leads_batch(records, fastapi)
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


def th_process_apm_leads_batch(records: list[RecordDatas], fastapi: ThFastapi):
    """
    Tối ưu hóa xử lý batch APM leads với:
    - Bulk database operations
    - Caching lookup data
    - Error handling per-record
    - Reduced database queries
    """
    th_start_time = time.time()
    results = []

    _logger.info(f"Starting batch processing for {len(records)} records")

    # Phân loại records theo loại operation
    th_create_records = []
    th_update_records = []
    th_delete_records = []

    for record in records:
        if record.id_b2b == 0:
            th_create_records.append(record)
        elif record.id_b2b != 0 and record.th_data_apm:
            th_update_records.append(record)
        elif record.id_b2b != 0 and not record.th_data_apm:
            th_delete_records.append(record)

    _logger.info(f"Classified records: {len(th_create_records)} creates, {len(th_update_records)} updates, {len(th_delete_records)} deletes")

    # Xử lý create operations với batch optimization
    if th_create_records:
        th_create_start = time.time()
        th_create_results = th_process_create_batch(th_create_records, fastapi)
        results.extend(th_create_results)
        _logger.info(f"Create batch completed in {time.time() - th_create_start:.2f}s")

    # Xử lý update operations
    if th_update_records:
        th_update_start = time.time()
        th_update_results = th_process_update_batch(th_update_records, fastapi)
        results.extend(th_update_results)
        _logger.info(f"Update batch completed in {time.time() - th_update_start:.2f}s")

    # Xử lý delete operations
    if th_delete_records:
        th_delete_start = time.time()
        th_delete_results = th_process_delete_batch(th_delete_records, fastapi)
        results.extend(th_delete_results)
        _logger.info(f"Delete batch completed in {time.time() - th_delete_start:.2f}s")

    th_total_time = time.time() - th_start_time
    _logger.info(f"Total batch processing completed in {th_total_time:.2f}s")

    return results


def th_process_create_batch(th_create_records: list[RecordDatas], fastapi: ThFastapi):
    """
    Tối ưu hóa xử lý batch create operations với:
    - Bulk lookup cho countries và products
    - Batch create partners và apm leads
    - Error handling per-record
    """
    results = []

    # Thu thập tất cả country codes cần lookup
    th_country_codes = set()
    th_product_template_ids = set()

    for record in th_create_records:
        values = record.th_data_apm.model_dump(exclude_none=True, exclude_unset=True)
        partner_info = values.get('partner_info')
        if partner_info and partner_info.get('country_id'):
            th_country_codes.add(partner_info['country_id'])
        if values.get('th_product_ids'):
            th_product_template_ids.add(values['th_product_ids'])

    # Bulk lookup countries và products
    th_countries_cache = {}
    if th_country_codes:
        countries = fastapi.env['res.country'].search([('code', 'in', list(th_country_codes))])
        th_countries_cache = {country.code: country.id for country in countries}

    th_products_cache = {}
    if th_product_template_ids:
        products = fastapi.env['product.product'].search([('product_tmpl_id', 'in', list(th_product_template_ids))])
        for product in products:
            if product.product_tmpl_id.id not in th_products_cache:
                th_products_cache[product.product_tmpl_id.id] = []
            th_products_cache[product.product_tmpl_id.id].append(product.id)

    # Xử lý từng record với cached data
    for record in th_create_records:
        try:
            values = record.th_data_apm.model_dump(exclude_none=True, exclude_unset=True)
            partner_info = values.pop('partner_info', None)
            partner_id = False

            # Xử lý partner creation với cached country data
            if not values.get('th_partner_id') and partner_info:
                # Tối ưu hóa partner creation
                partner_id = th_create_partner_optimized(partner_info, th_countries_cache, fastapi)
                values['th_partner_id'] = partner_id

            # Xử lý product mapping với cached data
            if values.get('th_product_ids') and values['th_product_ids'] in th_products_cache:
                values['th_product_ids'] = th_products_cache[values['th_product_ids']]

            # Tạo apm lead với optimized method
            apm_lead = fastapi.env['th.apm'].sudo().with_context(th_sync=True).create(values)

            results.append({
                "status": "success",
                "response": 'ok',
                "id": apm_lead.id,
                'th_partner_id': partner_id,
            })

        except Exception as e:
            results.append({
                "status": "error",
                "response": str(e),
                "id": None,
                'th_partner_id': None,
            })

    return results


def th_process_update_batch(th_update_records: list[RecordDatas], fastapi: ThFastapi):
    """
    Tối ưu hóa xử lý batch update operations
    """
    results = []

    # Bulk browse tất cả apm records cần update
    th_apm_ids = [record.id_b2b for record in th_update_records]
    th_apm_records = fastapi.env['th.apm'].browse(th_apm_ids)
    th_apm_dict = {apm.id: apm for apm in th_apm_records if apm.exists()}

    for record in th_update_records:
        try:
            if record.id_b2b in th_apm_dict:
                th_apm = th_apm_dict[record.id_b2b]
                data_to_update = record.th_data_apm.model_dump(exclude_none=True, exclude_unset=True)
                th_apm.sudo().with_context(th_sync=True).th_write_th_apm(data_to_update)

                results.append({
                    "status": "success",
                    "response": "updated",
                    "id": record.id_b2b,
                })
            else:
                results.append({
                    "status": "error",
                    "response": "Record not found",
                    "id": record.id_b2b,
                })

        except Exception as e:
            results.append({
                "status": "error",
                "response": str(e),
                "id": record.id_b2b,
            })

    return results


def th_process_delete_batch(th_delete_records: list[RecordDatas], fastapi: ThFastapi):
    """
    Tối ưu hóa xử lý batch delete operations
    """
    results = []

    # Bulk browse tất cả apm records cần delete
    th_apm_ids = [record.id_b2b for record in th_delete_records]
    th_apm_records = fastapi.env['th.apm'].browse(th_apm_ids)
    th_apm_dict = {apm.id: apm for apm in th_apm_records if apm.exists()}

    for record in th_delete_records:
        try:
            if record.id_b2b in th_apm_dict:
                th_apm = th_apm_dict[record.id_b2b]

                # Kiểm tra điều kiện xóa
                if th_apm.th_partner_id and th_apm.th_type_of_care == 'advise':
                    results.append({
                        "status": "error",
                        "response": f"Không thể xóa cơ hội {th_apm.id} vì đã được bàn giao cho tư vấn chăm sóc.",
                        "id": record.id_b2b,
                    })
                    continue

                th_apm.sudo().with_context(th_sync=True).unlink()
                results.append({
                    "status": "success",
                    "response": "deleted",
                    "id": record.id_b2b,
                })
            else:
                results.append({
                    "status": "error",
                    "response": "Record not found",
                    "id": record.id_b2b,
                })

        except Exception as e:
            results.append({
                "status": "error",
                "response": str(e),
                "id": record.id_b2b,
            })

    return results


# @router.put("/api/ApmLead/{id}")
def update_apm_lead(
        ApmLeadDatas: APMLeadDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            th_apm = fastapi.env['th.apm'].browse(id)
            if not th_apm.exists():
                raise HTTPException(status_code=404, detail="Không tìm thấy cơ hội.")
            data_to_update = ApmLeadDatas.model_dump(exclude_none=True, exclude_unset=True)
            th_apm.sudo().with_context(th_sync=True).th_write_th_apm(data_to_update)
            return

    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


# @router.delete("/api/ApmLead/{id}")
def delete_apm_lead(
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            apm_leads = fastapi.env['th.apm'].browse(id)
            if not apm_leads:
                raise HTTPException(status_code=404, detail="Không tìm thấy các cơ hội.")

            for apm_lead in apm_leads:
                if apm_lead.th_partner_id and apm_lead.th_type_of_care == 'advise':
                    raise HTTPException(
                        status_code=403,
                        detail=f"Không thể xóa cơ hội {apm_lead.id} vì đã được bàn giao cho tư vấn chăm sóc."
                    )

            apm_leads.sudo().with_context(th_sync=True).unlink()
            return {"status": "success", "message": "Xóa thành công các bản ghi APM."}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))
@router.post("/api/thapmtraitvalue")
def create_apm_trait_value(
        apm_trait_value_data: ApmTraitValueDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    """Tạo bản ghi mới trong 'th.apm.trait.value'."""
    try:
        if fastapi:
            apm_trait_value = fastapi.env['th.apm.trait.value'].create(
                apm_trait_value_data.model_dump(exclude_unset=True, exclude_none=True)
            )
            return {'id': apm_trait_value.id}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/api/thapmtraitvalue/{id}")
def write_apm_trait_value(
        apm_trait_value_data: ApmTraitValueDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    """Cập nhật bản ghi theo ID."""
    try:
        if fastapi:
            apm_trait_value = fastapi.env['th.apm.trait.value'].browse(id)
            if not apm_trait_value.exists():
                raise HTTPException(status_code=404, detail="APM Trait not found.")
            apm_trait_value.sudo().write(
                apm_trait_value_data.model_dump(exclude_unset=True, exclude_none=True)
            )
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/thapmtraitvalue/{id}")
def delete_apm_trait_value(
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    """Xóa bản ghi theo ID."""
    try:
        if fastapi:
            apm_trait = fastapi.env['th.apm.trait.value'].browse(id)
            if not apm_trait.exists():
                raise HTTPException(status_code=404, detail="APM Trait not found.")
            apm_trait.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/api/thapmtrait")
def create_apm_trait(
        apm_trait_data: ApmTraitDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            apm_trait = fastapi.env['th.apm.trait'].create(apm_trait_data.model_dump(exclude_unset=True,exclude_none=True))
            return {
                'id': apm_trait.id
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/thapmtrait/{id}")
def write_apm_trait(
        apm_trait_data: ApmTraitDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            apm_trait = fastapi.env['th.apm.trait'].browse(id)
            if not apm_trait.exists():
                raise HTTPException(status_code=404, detail="APM Trait not found.")
            apm_trait_datas = apm_trait_data.model_dump(exclude_unset=True,exclude_none=True)
            # data_for_apm_trait = {
            #     'name': apm_trait_data.name if apm_trait_data.name else apm_trait.name,
            #     'th_origin_id': apm_trait_data.th_origin_id if apm_trait_data.th_origin_id else apm_trait.th_origin_id.id,
            # }
            apm_trait.sudo().write(apm_trait_datas)
            # apm_trait.th_apm_trait_value_ids.unlink()
            # for rec in apm_trait_data.th_apm_trait_value_ids:
            #     apm_trait_value = fastapi.env['th.apm.trait.value'].with_context(
            #         default_th_apm_trait_id=apm_trait.id).create(rec.dict())
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/thapmtrait/{id}")
def delete_apm_trait(
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            apm_trait = fastapi.env['th.apm.trait'].browse(id)
            if not apm_trait.exists():
                raise HTTPException(status_code=404, detail="APM Trait not found.")
            apm_trait.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/thapmcontacttrait")
def create_apm_contact_trait(
        apm_contact_trait_data: ApmContactTraitDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            data_for_trait = {
                'th_origin_id': apm_contact_trait_data.th_origin_id,
                'th_apm_trait_id': apm_contact_trait_data.th_apm_trait_id,
                'th_apm_trait_value_ids': apm_contact_trait_data.th_apm_trait_value_ids,
            }
            apm_trait = fastapi.env['th.apm.contact.trait'].create(data_for_trait)
            return {
                'id': apm_trait.id
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/thapmcontacttrait/{id}")
def write_apm_trait(
        apm_trait_data: ApmContactTraitDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            apm_contact_trait = fastapi.env['th.apm.contact.trait'].browse(id)
            if not apm_contact_trait.exists():
                raise HTTPException(status_code=404, detail="APM Trait not found.")
            data_to_update = apm_trait_data.model_dump(exclude_unset=True,exclude_none=True)
            apm_contact_trait.sudo().write(data_to_update)
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/thapmcontacttrait/{id}")
def delete_apm_trait(
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            apm_contact_trait = fastapi.env['th.apm.contact.trait'].browse(id)
            if not apm_contact_trait.exists():
                raise HTTPException(status_code=404, detail="APM Contact Trait not found.")
            apm_contact_trait.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/apmteam")
def create_apm_team(
        ApmTeamDatas: ApmTeamDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            apm_team = fastapi.env['th.apm.team'].th_create_apm_team(datas=ApmTeamDatas)
            return {'id': apm_team.id}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.put("/api/apmteam/{id}")
def write_apm_team(
        ApmTeamDatas: ApmTeamDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            apm_team = fastapi.env['th.apm.team'].browse(id)
            if not apm_team.exists():
                raise HTTPException(status_code=404, detail="Team not found.")
            data_to_update = ApmTeamDatas.model_dump(exclude_unset=True,exclude_none=True)
            apm_team.write(data_to_update)
            return {
                'id': apm_team.id,
            }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/apmteam/{id}")
def delete_apm_team(
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            apm_team = fastapi.env['th.apm.team'].browse(id)
            if not apm_team.exists():
                raise HTTPException(status_code=404, detail="Exempted Subject not found.")

            apm_team.unlink()
            return {"detail": f"Exempted Subject with id {id} has been deleted."}
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/api/saleorders")
def create_sale_orders(
        sale_order_data: list[CreateRecordDatas],
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)]
):
    try:
        if fastapi:
            responses = []
            for rec in sale_order_data:
                values = rec.model_dump()
                if values.get('id_b2b', False):
                    response = write_sale_order(rec.th_data_sale, fastapi, values['id_b2b'])
                    responses.append({
                        'id': values.get('id_b2b', False),
                    })
                else:
                    th_data_sale = values.get('th_data_sale', {})
                    th_srm_lead_id = th_data_sale.get('th_srm_lead_id')
                    if th_srm_lead_id:
                        srm = fastapi.env['th.student'].search([('id', '=', th_srm_lead_id)], limit=1)
                        if srm and srm.th_partner_id:
                            th_data_sale['partner_id'] = srm.th_partner_id.id
                            th_data_sale['partner_invoice_id'] = srm.th_partner_id.id
                            th_data_sale['partner_shipping_id'] = srm.th_partner_id.id
                    elif th_data_sale.get('th_apm_id'):
                        th_apm_id = th_data_sale['th_apm_id']
                        apm = fastapi.env['th.apm'].search([('id', '=', th_apm_id)], limit=1)
                        if apm and apm.th_partner_id:
                            th_data_sale['partner_id'] = apm.th_partner_id.id
                            th_data_sale['partner_invoice_id'] = apm.th_partner_id.id
                            th_data_sale['partner_shipping_id'] = apm.th_partner_id.id
                    order_line_data = values['th_data_sale']['order_lines']
                    del values['th_data_sale']['order_lines']
                    sale_order = fastapi.env['sale.order'].sudo().with_context(th_sync=True).create(
                        values['th_data_sale'])
                    line_ids = []
                    if order_line_data:
                        for order_line in order_line_data:
                            order_line['order_id'] = sale_order.id
                            if order_line['product_template_id'] and order_line['product_id']:
                                ol = fastapi.env['sale.order.line'].sudo().with_context(th_sync=True).create(order_line)
                            elif order_line['product_template_id'] and not order_line['product_id']:
                                product = fastapi.env['product.product'].search([('product_tmpl_id','=',order_line['product_template_id'])])
                                if product:
                                    order_line['product_id'] = product.id
                                    ol = fastapi.env['sale.order.line'].sudo().with_context(th_sync=True).create(order_line)
                            line_ids.append(ol.id)

                    responses.append({
                        'id': sale_order.id,
                        'line_ids': line_ids
                    })

            return responses
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


def write_sale_order(
        sale_order_data: SaleOrderDatas,
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            sale_order = fastapi.env['sale.order'].browse(id)
            if not sale_order.exists():
                raise HTTPException(status_code=404, detail="Không tìm thấy đơn hàng.")
            data_to_update = sale_order_data.model_dump(exclude_none=True, exclude_unset=True)
            if 'order_lines' in data_to_update.keys():
                order_line_data = data_to_update.pop('order_lines')
                line_ids = []
                if sale_order.th_status_payment_invoiced != 'paid':
                    if order_line_data:
                        sale_order.order_line.unlink()
                        for order_line in order_line_data:
                            order_line['order_id'] = sale_order.id
                            ol = fastapi.env['sale.order.line'].sudo().with_context(th_sync=True).create(order_line)
                            line_ids.append(ol.id)
            sale_order.sudo().with_context(th_sync=True).write(data_to_update)
            # return {
            #         'id': sale_order.id,
            #         'line_ids': line_ids if line_ids else False
            # }

            # if line_ids:
            #     return {
            #         'line_ids': line_ids
            #     }
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.delete("/api/saleorder/{id}")
def delete_sale_order(
        fastapi: Annotated[ThFastapi, Depends(authenticated_fastapi_endpoint)],
        id: int = Path(..., ge=0)
):
    try:
        if fastapi:
            sale_order = fastapi.env['sale.order'].browse(id)
            if not sale_order.exists():
                raise HTTPException(status_code=404, detail="Không tìm thấy đơn hàng.")
            sale_order.sudo().with_context(th_sync=True).unlink()
            return
    except UserError as e:
        raise HTTPException(status_code=400, detail=str(e))


def th_create_partner_optimized(partner_info: dict, th_countries_cache: dict, fastapi: ThFastapi) -> int:
    """
    Tối ưu hóa việc tạo partner với cached country data
    """
    try:
        # Xử lý country mapping với cache
        if partner_info.get('country_id') and partner_info['country_id'] in th_countries_cache:
            country_id = th_countries_cache[partner_info['country_id']]
            partner_info['country_id'] = country_id
            partner_info['th_country_id'] = country_id

        # Tạo partner
        partner = fastapi.env['res.partner'].create(partner_info)
        return partner.id

    except Exception as e:
        _logger.error(f"Error creating partner: {str(e)}")
        raise


def th_get_performance_summary(th_total_time: float, th_total_records: int) -> dict:
    """
    Tạo summary về hiệu suất xử lý
    """
    return {
        "total_time": round(th_total_time, 2),
        "total_records": th_total_records,
        "records_per_second": round(th_total_records / th_total_time if th_total_time > 0 else 0, 2),
        "avg_time_per_record": round(th_total_time / th_total_records if th_total_records > 0 else 0, 4)
    }
